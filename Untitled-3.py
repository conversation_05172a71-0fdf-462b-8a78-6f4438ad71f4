# Configuration: PySR model and hyperparameters
import numpy as np
from pysr import PySRRegressor
from sklearn.model_selection import train_test_split, KFold
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt

# Use all available CPU cores for PySR and appropriate parallelism:contentReference[oaicite:0]{index=0}
import os
n_cores = os.cpu_count() or 1
# Set procs to use all cores, and ensure enough populations for parallel search:contentReference[oaicite:1]{index=1}:contentReference[oaicite:2]{index=2}
pysr_procs = n_cores
pysr_populations = max(15, 3 * n_cores)  # 3x number of cores is recommended for diversity:contentReference[oaicite:3]{index=3}

# Increase ncycles_per_iteration to better utilize all cores (especially if cores idle):contentReference[oaicite:4]{index=4}:contentReference[oaicite:5]{index=5}
pysr_ncycles = 1000  # e.g., 1000 for ~8 cores; on clusters with >>10 cores, consider higher (e.g., 5000)

# Configure symbolic regression model (PySRRegressor)
model_config = {
    'niterations': 300,            # Number of iterations (generations) for final run (increase for complex problems)
    'populations': pysr_populations,
    'population_size': 50,         # Larger population for more diversity:contentReference[oaicite:6]{index=6}
    'ncycles_per_iteration': pysr_ncycles,
    'procs': pysr_procs,
    'parallelism': 'multithreading',  # Use multithreading by default:contentReference[oaicite:7]{index=7} (alternatively, 'multiprocessing')
    'random_state': 0,             # Reproducible results across runs:contentReference[oaicite:8]{index=8}
    'warm_start': False,           # Start fresh each fit (avoid state carry-over between fits):contentReference[oaicite:9]{index=9}
    'binary_operators': ['+', '-', '*', '/'],  # Use only necessary operators (avoid redundant ones):contentReference[oaicite:10]{index=10}
    'unary_operators': ['sin', 'cos'],        # Include relevant unary ops if needed (e.g., sin, cos for periodic components)
    'parsimony': 1e-4,             # Complexity penalty (helps avoid overfitting to noise):contentReference[oaicite:11]{index=11}
    'model_selection': 'best',    # Select final model balancing accuracy & complexity:contentReference[oaicite:12]{index=12} ('accuracy' for lowest loss)
    'turbo': True                 # Enable experimental speed-up (LoopVectorization):contentReference[oaicite:13]{index=13}
}
# Note: If dataset is large (>1000 samples), consider setting 'batching': True for faster evolution:contentReference[oaicite:14]{index=14}.
# model_config['batching'] = True

# Data Handling: load or generate dataset
# (Here we generate a synthetic dataset for demonstration; replace with real data as needed)
np.random.seed(0)
X = 2 * np.random.randn(1000, 5)  # e.g., 1000 samples, 5 features
# True relationship: y = 2.5*cos(x3) + x0^2 - 0.5 + noise
y_true_func = lambda X: 2.5 * np.cos(X[:, 3]) + X[:, 0]**2 - 0.5
y = y_true_func(X) + np.random.normal(scale=1.0, size=X.shape[0])  # add noise

# Ensure data types are float32 for performance (PySR default precision):contentReference[oaicite:15]{index=15}
X = X.astype(np.float32)
y = y.astype(np.float32)

# Split data into training and test sets for evaluation of final model:contentReference[oaicite:16]{index=16}
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=0)
print(f"Training samples: {X_train.shape[0]}, Test samples: {X_test.shape[0]}")

# Cross-Validation: evaluate model performance on training set via 5-fold CV (on training data):contentReference[oaicite:17]{index=17}
cv = KFold(n_splits=5, shuffle=True, random_state=0)
cv_scores = []
# We use a shorter training for CV to save time, and fresh model for each fold
cv_config = model_config.copy()
cv_config['niterations'] = 100   # fewer iterations for CV runs to reduce computation
cv_config['verbosity'] = 0      # silence verbose output during CV
for fold, (train_idx, val_idx) in enumerate(cv.split(X_train), start=1):
    X_tr, X_val = X_train[train_idx], X_train[val_idx]
    y_tr, y_val = y_train[train_idx], y_train[val_idx]
    model_cv = PySRRegressor(**cv_config)
    model_cv.fit(X_tr, y_tr)
    y_val_pred = model_cv.predict(X_val)
    mse_val = mean_squared_error(y_val, y_val_pred)
    cv_scores.append(mse_val)
    print(f"Fold {fold} MSE: {mse_val:.4f}")
# Report cross-validation performance
cv_mean = np.mean(cv_scores)
cv_std = np.std(cv_scores)
print(f"Average CV MSE: {cv_mean:.4f} (±{cv_std:.4f})")

# Training: fit PySR on the entire training data with full iterations
model_final = PySRRegressor(**model_config, verbosity=0)
model_final.fit(X_train, y_train)
# After fitting, examine the discovered equations and select the best
print(model_final)  # Show all candidate equations (Pareto front); '>>' marks the selected equation:contentReference[oaicite:18]{index=18}:contentReference[oaicite:19]{index=19}
best_sympy = model_final.sympy()  # Symbolic expression of the best model
print(f"Best discovered equation: {best_sympy}")
# Evaluate model performance on training vs test data
y_train_pred = model_final.predict(X_train)
y_test_pred = model_final.predict(X_test)
train_mse = mean_squared_error(y_train, y_train_pred)
test_mse = mean_squared_error(y_test, y_test_pred)
print(f"Training MSE (final model): {train_mse:.4f}")
print(f"Test MSE (final model): {test_mse:.4f}")

# Visualization: plot predictions vs actuals, and complexity vs loss trade-off
plt.figure(figsize=(6, 5))
plt.scatter(y_train, y_train_pred, color='blue', label='Train')
plt.scatter(y_test, y_test_pred, color='red', label='Test')
min_val = min(y.min(), y_train_pred.min(), y_test_pred.min())
max_val = max(y.max(), y_train_pred.max(), y_test_pred.max())
plt.plot([min_val, max_val], [min_val, max_val], 'k--')  # Ideal diagonal line
plt.xlabel("Actual")
plt.ylabel("Predicted")
plt.legend()
plt.title("Actual vs Predicted")
plt.show()

# Plot complexity vs loss for all discovered equations (Pareto front)
eq_df = model_final.equations_
eq_df_sorted = eq_df.sort_values("complexity")
plt.figure()
plt.plot(eq_df_sorted["complexity"], eq_df_sorted["loss"], marker='o')
plt.yscale("log")
plt.xlabel("Equation Complexity")
plt.ylabel("Loss (MSE)")
plt.title("Complexity vs Loss (Pareto front)")
plt.show()

# Future enhancements (for further development):
# - Use automated hyperparameter tuning (e.g., Optuna) to find optimal PySR parameters like parsimony or operator sets:contentReference[oaicite:20]{index=20}.
# - Evaluate each candidate equation on a validation set or via nested cross-validation to choose the best generalizing formula:contentReference[oaicite:21]{index=21}.
# - Integrate TensorBoard logging for monitoring PySR training progress (using PySR's logger support):contentReference[oaicite:22]{index=22}.
# - Incorporate domain knowledge with custom operators or constraints (e.g., limit exponent complexity or prevent nested trigonometric functions):contentReference[oaicite:23]{index=23}.
# - Run multiple PySR searches with different random seeds and ensemble or compare results for more robust equation discovery.
