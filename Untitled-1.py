#!/usr/bin/env python3
"""
Comprehensive and Enhanced PySR Analysis for Symbolic Regression

Features:
- Highly configurable parameters
- Group-based cross-validation
- Multiple target variables
- R² analysis across different regions
- Enhanced feature engineering options
- Custom PySR operators for domain-specific modeling
- Advanced model validation:
    - Residual analysis (mean, std, skewness, kurtosis, normality)
    - Equation complexity analysis
    - Feature importance (based on SymPy parsing)
    - Model stability analysis across folds
- Comprehensive visualizations:
    - Combined actual vs. predicted scatter plots
    - Fold-wise R² comparisons
    - Residual plots
    - Distribution comparisons of true and predicted values
    - Individual fold actual vs. predicted plots
    - Pareto front plots (complexity vs. score)
    - Plotting of individual equations against data
- Metrics export (text summary and detailed pickle)
- Model saving
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import GroupKFold
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.impute import SimpleImputer
from pysr import PySRRegressor
import warnings
import os
from datetime import datetime
import pickle
import sympy
from collections import Counter
from scipy.stats import shapiro # For normality test

warnings.filterwarnings('ignore')

# --- STATUS CONSTANTS ---
STATUS_FAILED_NO_EQUATIONS = "FAILED_NO_EQUATIONS"
STATUS_FAILED_NO_BEST_EQUATION = "FAILED_NO_BEST_EQUATION"
STATUS_FAILED_EXCEPTION = "FAILED_EXCEPTION"

# --- CONFIGURATIONS ---
# Data and Paths
DATA_PATH = 'Data_With_stats.csv' # Path to your input CSV file
OUTPUT_DIR_BASE = 'pysr_analysis_output_enhanced' # Base directory for all outputs
# Subdirectories will be created: <OUTPUT_DIR_BASE>/results, <OUTPUT_DIR_BASE>/plots, <OUTPUT_DIR_BASE>/models

# Feature and Target Columns
# Base features before any engineering
X_COLS_BASE = ['DNAcontent', 'amu', 'LET', 'Di']
MICRO_ODOSIMETRY_COLS = [
    'Std_Sim_Dose', 'Tracks_per_Nucleus', 'P_cells_hit', 'Dose_hitCells',
    'Std_Dose_hitCells', 'P_No_hit', 'P_one_hit', 'P_multi_hit'
]
Y_COLS = ['SFi', 'RBE10'] # Add 'MIDi' or other target column names if available and desired
GROUP_COLS = ['DNAcontent', 'amu', 'LET'] # Columns for GroupKFold

# Feature Engineering
USE_ENHANCED_FEATURES = True # Set to False to use only X_COLS_BASE + MICRO_ODOSIMETRY_COLS

# Cross-validation
N_SPLITS_CV = 5 # Number of folds for cross-validation

# PySR Settings
PYSR_NITERATIONS = 5      # Number of iterations for PySR to run
PYSR_POPULATIONS = 10         # Number of populations in island model (e.g., 5-20)
PYSR_POPULATION_SIZE = 30     # Number of individuals in each population (e.g., 30-100)
PYSR_MAXSIZE = 20             # Maximum complexity of equations
PYSR_TIMEOUT_IN_SECONDS = 600 # Timeout for PySR per fold/target (e.g., 600 for 10 min)
PYSR_PARSIMONY = 0.005        # Parsimony coefficient (e.g., 0.001 to 0.01)
PYSR_BINARY_OPERATORS = ["+", "-", "*", "/", "pow"]
PYSR_UNARY_OPERATORS = [
    "exp", "log", "sqrt", "abs", "sin", "cos", "tanh",
    "neg", "square", "cube", "sigmoid", "gaussian_like" # Added custom/common ones
]
# Define custom operators as SymPy functions
# Note: PySR needs to be able to convert these to Julia. Simple lambdas often work.
# For more complex functions, you might need to define them in Julia.
CUSTOM_UNARY_OPERATORS_SYMPY = {
    "square": lambda x: x**2,
    "cube": lambda x: x**3,
    "sigmoid": lambda x: 1 / (1 + sympy.exp(-x)),
    "gaussian_like": lambda x: sympy.exp(-(x**2)) # Example: exp(-x^2)
    # "safe_log": lambda x: sympy.log(x + 1e-9), # PySR has internal safe log
    # "inv": lambda x: 1/x # PySR's division handles this
}
PYSR_EXTRA_SYMPY_MAPPINGS = CUSTOM_UNARY_OPERATORS_SYMPY
PYSR_COMPLEXITY_OF_OPERATORS = { # Optional: Assign custom complexities
    "exp": 2, "log": 2, "pow": 2, "sigmoid": 3, "gaussian_like": 3
}
PYSR_LOSS_FUNCTION = "L2DistLoss()" # Default is MSE, equivalent to "loss(prediction, target) = (prediction - target)^2"
PYSR_PROCS = 0                # Number of processes (0 for all cores)
PYSR_DELETE_TEMPFILES = True  # Delete temp files after run (False for debugging)
PYSR_RANDOM_STATE = 42
PYSR_VERBOSITY = 0            # 0 for quiet, 1 for normal, >1 for debug
PYSR_PROGRESS = True

# Analysis Settings
MIN_SAMPLES_FOR_REGIONAL_R2 = 20
TOP_N_FEATURES_TO_DISPLAY_IMPORTANCE = 10

# --- END CONFIGURATIONS ---

class PySRComprehensiveAnalysis:
    def __init__(self, config):
        """Initialize the analysis with data loading and preprocessing."""
        self.config = config
        self.df = None
        self.X = None
        self.y_dict = {}
        self.groups = None
        self.feature_names = []
        self.x_cols_original = self.config.X_COLS_BASE + self.config.MICRO_ODOSIMETRY_COLS
        
        self.results = {} # Stores fold_results, models, equations per target
        self.combined_results = {} # Stores combined y_true, y_pred, metrics per target
        self.validation_results = {} # Stores enhanced validation metrics

        # Create output directories
        self.output_dir_results = os.path.join(self.config.OUTPUT_DIR_BASE, 'results')
        self.output_dir_plots = os.path.join(self.config.OUTPUT_DIR_BASE, 'plots')
        self.output_dir_models = os.path.join(self.config.OUTPUT_DIR_BASE, 'models')
        os.makedirs(self.output_dir_results, exist_ok=True)
        os.makedirs(self.output_dir_plots, exist_ok=True)
        os.makedirs(self.output_dir_models, exist_ok=True)

        print("Loading and preprocessing data...")
        self.load_and_preprocess_data()

    def load_and_preprocess_data(self):
        """Load and preprocess the data with enhanced feature engineering."""
        try:
            self.df = pd.read_csv(self.config.DATA_PATH)
        except FileNotFoundError:
            print(f"Error: Data file not found at {self.config.DATA_PATH}")
            raise
        print(f"Loaded data shape: {self.df.shape}")

        # Check for required columns
        current_x_cols = list(self.x_cols_original) # Start with base X columns
        
        # Feature Engineering (if enabled)
        if self.config.USE_ENHANCED_FEATURES:
            print("\nPerforming enhanced feature engineering...")
            
            # Create interaction features (once)
            interaction_definitions = {
                'LET_Di_interaction': (['LET', 'Di'], lambda df: df['LET'] * df['Di']),
                'amu_LET_ratio': (['amu', 'LET'], lambda df: df['amu'] / (df['LET'] + 1e-8)),
                'DNAcontent_Di_product': (['DNAcontent', 'Di'], lambda df: df['DNAcontent'] * df['Di'])
            }
            for new_col, (base_cols, func) in interaction_definitions.items():
                if all(c in self.df.columns for c in base_cols):
                    self.df[new_col] = func(self.df)
                else:
                    print(f"Warning: Not all base columns {base_cols} for '{new_col}' found. Skipping this engineered feature.")

            # Create polynomial and log features for specific columns
            cols_for_poly_log = ['LET', 'Di', 'amu', 'DNAcontent']
            for col in cols_for_poly_log:
                if col in self.df.columns:
                    # Polynomial features (squared terms)
                    self.df[f'{col}_squared'] = self.df[col] ** 2
                    
                    # Log-transformed features (add epsilon for stability with zero/negative values)
                    self.df[f'log_{col}'] = np.log(self.df[col].abs() + 1e-8) # Use abs() if features can be negative
                else:
                    print(f"Warning: Base column '{col}' for polynomial/log feature engineering not found. Skipping these engineered features for '{col}'.")

            # Update feature columns to include engineered features that were successfully created
            engineered_feature_candidates = [
                'LET_Di_interaction', 'amu_LET_ratio', 'DNAcontent_Di_product',
                'LET_squared', 'Di_squared', 'amu_squared', 'DNAcontent_squared',
                'log_LET', 'log_Di', 'log_amu', 'log_DNAcontent'
            ]
            self.x_cols_enhanced = [col for col in engineered_feature_candidates if col in self.df.columns]
            current_x_cols.extend(self.x_cols_enhanced)
            current_x_cols = sorted(list(set(current_x_cols))) # Remove duplicates and sort
            print(f"Using enhanced features: {len(current_x_cols)} features")
        else:
            print(f"Using original features: {len(current_x_cols)} features")
        
        self.feature_names = current_x_cols

        # Validate all required columns are present
        missing_cols = []
        for col_list_name, col_list in zip(
            ["Feature (X)", "Target (Y)", "Grouping"],
            [self.feature_names, self.config.Y_COLS, self.config.GROUP_COLS]
        ):
            for col in col_list:
                if col not in self.df.columns:
                    missing_cols.append(f"{col} (for {col_list_name})")
        
        if missing_cols:
            print(f"\nError: Missing critical columns in the dataset:")
            for mc in missing_cols:
                print(f"- {mc}")
            print(f"Available columns: {list(self.df.columns)}")
            raise ValueError("Critical columns missing from the dataset.")

        # Remove rows with missing values in essential columns
        essential_cols = list(set(self.feature_names + self.config.Y_COLS + self.config.GROUP_COLS))
        self.df = self.df.dropna(subset=essential_cols)
        print(f"Data shape after removing NaN from essential columns: {self.df.shape}")
        if self.df.empty:
            raise ValueError("DataFrame is empty after removing NaN values. Check data quality or column definitions.")

        # Extract features and targets
        self.X = self.df[self.feature_names].values
        self.y_dict = {col: self.df[col].values for col in self.config.Y_COLS}
        
        # Data quality checks for features
        print("\nData quality checks for features (X):")
        if np.isinf(self.X).any():
            print("Warning: Infinite values found in features. Replacing with NaN.")
            self.X = np.where(np.isinf(self.X), np.nan, self.X)
        if np.any(np.abs(self.X) > 1e12): # Check for extremely large values
            print("Warning: Extremely large values found in features. Replacing with NaN.")
            self.X = np.where(np.abs(self.X) > 1e12, np.nan, self.X)
        
        # Handle any remaining NaN values in X by median imputation
        if np.isnan(self.X).any():
            print("Warning: NaN values found in features after processing. Applying median imputation.")
            imputer = SimpleImputer(strategy='median')
            self.X = imputer.fit_transform(self.X)
        
        # Create groups for cross-validation
        try:
            self.groups = self.df[self.config.GROUP_COLS].apply(
                lambda x: "_".join(map(str, x)), axis=1
            ).values
        except KeyError as e:
            print(f"Error creating groups: One of the group columns {self.config.GROUP_COLS} not found: {e}")
            raise

        print(f"\nFinal data summary:")
        print(f"Number of unique groups: {len(np.unique(self.groups))}")
        print(f"Features shape: {self.X.shape}")
        print(f"Feature names ({len(self.feature_names)}): {self.feature_names}")
        for col in self.config.Y_COLS:
            if col in self.y_dict:
                 print(f"{col} shape: {self.y_dict[col].shape}, range: [{self.y_dict[col].min():.3f}, {self.y_dict[col].max():.3f}]")
            else:
                print(f"Warning: Target column {col} not found in y_dict.")


    def create_pysr_model(self, target_name_for_temp_file="pysr_temp"):
        """Create a PySR model using configured parameters."""
        return PySRRegressor(
            niterations=self.config.PYSR_NITERATIONS,
            populations=self.config.PYSR_POPULATIONS,
            population_size=self.config.PYSR_POPULATION_SIZE,
            binary_operators=self.config.PYSR_BINARY_OPERATORS,
            unary_operators=self.config.PYSR_UNARY_OPERATORS,
            extra_sympy_mappings=self.config.PYSR_EXTRA_SYMPY_MAPPINGS,
            complexity_of_operators=self.config.PYSR_COMPLEXITY_OF_OPERATORS,
            maxsize=self.config.PYSR_MAXSIZE,
            loss=self.config.PYSR_LOSS_FUNCTION,
            parsimony=self.config.PYSR_PARSIMONY,
            timeout_in_seconds=self.config.PYSR_TIMEOUT_IN_SECONDS,
            random_state=self.config.PYSR_RANDOM_STATE,
            verbosity=self.config.PYSR_VERBOSITY,
            progress=self.config.PYSR_PROGRESS,
            temp_equation_file=True, # Recommended
            tempdir=os.path.join(self.output_dir_results, "pysr_temp_files"), # Store temp files in a subfolder
            # equation_file=os.path.join(self.output_dir_results, f"equations_{target_name_for_temp_file}.csv"), # Removed: Not a valid kwarg for this PySR version
            delete_tempfiles=self.config.PYSR_DELETE_TEMPFILES,
            procs=self.config.PYSR_PROCS,
            # feature_names_in=self.feature_names, # Removed: Not a valid kwarg for this PySR version
            # multithreading=True, # PySR handles this internally with procs            
            # julia_project=None # Assumes PySR Julia environment is set up
        )

    def perform_cross_validation(self):
        """Perform group-based cross-validation for all targets."""
        n_splits = self.config.N_SPLITS_CV
        print(f"\nStarting {n_splits}-fold group-based cross-validation...")

        gkf = GroupKFold(n_splits=n_splits)

        for target in self.config.Y_COLS:
            self.results[target] = {
                'fold_results': [], 'models': [], 'equations_dfs': [], 'best_equations_str': []
            }
            self.combined_results[target] = {
                'y_true_all': [], 'y_pred_all': [], 'fold_indices_all': [], 'test_set_indices_all': []
            }

        for fold_idx, (train_idx, test_idx) in enumerate(gkf.split(self.X, groups=self.groups)):
            print(f"\n--- Fold {fold_idx + 1}/{n_splits} ---")
            X_train, X_test = self.X[train_idx], self.X[test_idx]

            for target in self.config.Y_COLS:
                print(f"Training for target: {target}...")
                y_train = self.y_dict[target][train_idx]
                y_test = self.y_dict[target][test_idx]

                model_identifier = f"{target}_fold_{fold_idx + 1}"
                model = self.create_pysr_model(target_name_for_temp_file=model_identifier)

                try:
                    print(f"  Training data shape: X={X_train.shape}, y={y_train.shape}. Target range: [{y_train.min():.3f}, {y_train.max():.3f}]")
                    if np.any(np.isnan(X_train)) or np.any(np.isnan(y_train)):
                        raise ValueError(f"NaN values found in training data for {target} (Fold {fold_idx+1}) before fit.")
                    if np.any(np.isinf(X_train)) or np.any(np.isinf(y_train)):
                        raise ValueError(f"Infinite values found in training data for {target} (Fold {fold_idx+1}) before fit.")

                    print(f"  Starting PySR fitting for {target}...")
                    model.fit(X_train, y_train, X_features=self.feature_names) # Pass feature names to fit
                    print(f"  PySR fitting completed for {target}.")

                    if not hasattr(model, 'equations_') or model.equations_ is None or model.equations_.empty:
                        print(f"  Warning: PySR failed to find any equations for {target} in Fold {fold_idx + 1}.")
                        best_equation_str = STATUS_FAILED_NO_EQUATIONS
                        y_pred = np.full_like(y_test, np.nan) # Predictions are not possible
                        equations_df = pd.DataFrame()
                    else:
                        equations_df = model.get_best() # Get the single best equation by score (loss+parsimony)
                        if equations_df.empty:
                             best_equation_str = STATUS_FAILED_NO_BEST_EQUATION
                             y_pred = np.full_like(y_test, np.nan)
                        else:
                            best_equation_str = str(equations_df['sympy_format'].iloc[0])
                            print(f"  Making predictions for {target} using best equation...")
                            y_pred = model.predict(X_test) # Predicts using the equation with the best score

                    # Handle NaN/inf in predictions
                    valid_pred_mask = ~(np.isnan(y_pred) | np.isinf(y_pred))
                    if np.sum(valid_pred_mask) == 0 and len(y_test) > 0 : # only raise error if y_test is not empty
                        print(f"  Warning: All predictions are invalid for {target}. Setting metrics to NaN.")
                        r2, mse, mae = np.nan, np.nan, np.nan
                        y_test_clean, y_pred_clean = y_test, y_pred # Keep original for storage
                    elif np.sum(valid_pred_mask) < len(y_test) and len(y_test) > 0:
                        print(f"  Warning: Some predictions are invalid. Using {np.sum(valid_pred_mask)}/{len(y_test)} valid predictions for metrics.")
                        y_test_clean = y_test[valid_pred_mask]
                        y_pred_clean = y_pred[valid_pred_mask]
                        if len(y_test_clean) > 0:
                            r2 = r2_score(y_test_clean, y_pred_clean)
                            mse = mean_squared_error(y_test_clean, y_pred_clean)
                            mae = mean_absolute_error(y_test_clean, y_pred_clean)
                        else: # All predictions became invalid after filtering
                            r2, mse, mae = np.nan, np.nan, np.nan
                    elif len(y_test) == 0: # No test samples
                        r2, mse, mae = np.nan, np.nan, np.nan
                        y_test_clean, y_pred_clean = y_test, y_pred
                    else: # All predictions are valid
                        y_test_clean, y_pred_clean = y_test, y_pred
                        r2 = r2_score(y_test_clean, y_pred_clean)
                        mse = mean_squared_error(y_test_clean, y_pred_clean)
                        mae = mean_absolute_error(y_test_clean, y_pred_clean)
                    
                    num_equations_found = len(model.equations_) if hasattr(model, 'equations_') and model.equations_ is not None else 0

                    fold_result_data = {
                        'fold': fold_idx, 'r2': r2, 'mse': mse, 'mae': mae,
                        'y_true': y_test, 'y_pred': y_pred, # Store original y_pred
                        'best_equation_str': best_equation_str,
                        'train_size': len(train_idx), 'test_size': len(test_idx),
                        'num_equations_found': num_equations_found
                    }
                    self.results[target]['fold_results'].append(fold_result_data)
                    self.results[target]['models'].append(model) # Store the fitted model object                    
                    
                    # Store the full equations DataFrame from the model
                    full_equations_df_for_fold = model.equations_ if hasattr(model, 'equations_') and model.equations_ is not None else pd.DataFrame()
                    self.results[target]['equations_dfs'].append(full_equations_df_for_fold)
                    
                    # Save the full equations DataFrame to a CSV if equations were found (replaces functionality of 'equation_file' constructor arg)
                    if not full_equations_df_for_fold.empty:
                        eq_csv_path = os.path.join(self.output_dir_results, f"equations_{model_identifier}.csv")
                        try:
                            full_equations_df_for_fold.to_csv(eq_csv_path, index=False)
                            print(f"  Equations for {target} Fold {fold_idx + 1} saved to {eq_csv_path}")
                        except Exception as e_csv:
                            print(f"  Warning: Could not save equations DataFrame to CSV for {target} Fold {fold_idx + 1}: {e_csv}")
                    self.results[target]['best_equations_str'].append(best_equation_str)

                    # Store for combined analysis (only valid ones)
                    if np.sum(valid_pred_mask) > 0:
                        self.combined_results[target]['y_true_all'].extend(y_test_clean)
                        self.combined_results[target]['y_pred_all'].extend(y_pred_clean)
                        self.combined_results[target]['fold_indices_all'].extend([fold_idx] * len(y_test_clean))
                        # Store original test indices for later mapping to features if needed
                        self.combined_results[target]['test_set_indices_all'].extend(test_idx[valid_pred_mask])


                    print(f"  {target} Fold {fold_idx + 1} - R²: {r2:.4f}, MSE: {mse:.4f}, MAE: {mae:.4f}, Equations Found: {num_equations_found}")
                    if best_equation_str not in [STATUS_FAILED_NO_EQUATIONS, STATUS_FAILED_NO_BEST_EQUATION]:
                         print(f"    Best Eq: {best_equation_str[:100]}...") # Print snippet

                except Exception as e:
                    print(f"  Error training/evaluating {target} in fold {fold_idx + 1}: {str(e)}")
                    import traceback
                    print(f"  Traceback: {traceback.format_exc()}")
                    fold_result_data = {
                        'fold': fold_idx, 'r2': np.nan, 'mse': np.nan, 'mae': np.nan,
                        'y_true': y_test, 'y_pred': np.full_like(y_test, np.nan),
                        'best_equation_str': STATUS_FAILED_EXCEPTION,
                        'train_size': len(train_idx), 'test_size': len(test_idx),
                        'num_equations_found': 0, 'error': str(e)
                    }
                    self.results[target]['fold_results'].append(fold_result_data)
                    self.results[target]['models'].append(None) # No model
                    self.results[target]['equations_dfs'].append(pd.DataFrame())
                    self.results[target]['best_equations_str'].append(STATUS_FAILED_EXCEPTION)


        print("\nCross-validation completed!")

    def calculate_combined_metrics(self):
        """Calculate overall metrics from combined cross-validation results."""
        print("\nCalculating combined metrics from all folds...")
        for target in self.config.Y_COLS:
            if not self.combined_results[target]['y_true_all']:
                print(f"No valid results to combine for target {target}.")
                self.combined_results[target]['combined_metrics'] = None
                continue

            y_true = np.array(self.combined_results[target]['y_true_all'])
            y_pred = np.array(self.combined_results[target]['y_pred_all'])

            if len(y_true) == 0: # Should not happen if y_true_all was populated
                print(f"Zero samples for combined metrics for target {target}.")
                self.combined_results[target]['combined_metrics'] = None
                continue

            combined_r2 = r2_score(y_true, y_pred)
            combined_mse = mean_squared_error(y_true, y_pred)
            combined_mae = mean_absolute_error(y_true, y_pred)

            self.combined_results[target]['combined_metrics'] = {
                'r2': combined_r2, 'mse': combined_mse, 'mae': combined_mae,
                'n_samples': len(y_true)
            }
            print(f"{target} Combined - R²: {combined_r2:.4f}, MSE: {combined_mse:.4f}, MAE: {combined_mae:.4f} (on {len(y_true)} samples)")


    def analyze_regional_r2(self):
        """Analyze R² across different regions of numerical features.
        Note: This analysis uses feature values from the original test sets corresponding
        to the combined predictions.
        """
        print("\nAnalyzing regional R² performance...")
        for target in self.config.Y_COLS:
            if not self.combined_results[target]['y_true_all'] or \
               not self.combined_results[target]['test_set_indices_all']:
                print(f"Skipping regional R² for {target} due to missing combined results or test indices.")
                continue

            y_true_combined = np.array(self.combined_results[target]['y_true_all'])
            y_pred_combined = np.array(self.combined_results[target]['y_pred_all'])
            # Get the original indices of the test samples used for combined predictions
            test_indices_combined = np.array(self.combined_results[target]['test_set_indices_all'])
            
            # Extract corresponding feature values from the original dataset using these indices
            X_test_combined = self.df.iloc[test_indices_combined][self.feature_names].values

            regional_analysis_results = {}
            for i, feature_name in enumerate(self.feature_names):
                feature_values_for_analysis = X_test_combined[:, i]
                
                if len(feature_values_for_analysis) < self.config.MIN_SAMPLES_FOR_REGIONAL_R2 * 2: # Need enough for quartiles
                    continue

                try:
                    q25, q50, q75 = np.percentile(feature_values_for_analysis, [25, 50, 75])
                except IndexError: # Happens if too few samples
                    print(f"  Skipping regional analysis for {feature_name} due to too few samples for quartiles.")
                    continue

                regions = {
                    f'{feature_name}_Q1 (<= {q25:.2f})': feature_values_for_analysis <= q25,
                    f'{feature_name}_Q2 ({q25:.2f} < x <= {q50:.2f})': (feature_values_for_analysis > q25) & (feature_values_for_analysis <= q50),
                    f'{feature_name}_Q3 ({q50:.2f} < x <= {q75:.2f})': (feature_values_for_analysis > q50) & (feature_values_for_analysis <= q75),
                    f'{feature_name}_Q4 (> {q75:.2f})': feature_values_for_analysis > q75
                }

                feature_regional_data = {}
                for region_name, region_mask in regions.items():
                    if np.sum(region_mask) >= self.config.MIN_SAMPLES_FOR_REGIONAL_R2:
                        y_true_region = y_true_combined[region_mask]
                        y_pred_region = y_pred_combined[region_mask]
                        
                        if len(y_true_region) > 1: # Need at least 2 samples for r2_score
                            r2_region = r2_score(y_true_region, y_pred_region)
                            feature_regional_data[region_name] = {
                                'r2': r2_region,
                                'n_samples': len(y_true_region),
                                'feature_min': feature_values_for_analysis[region_mask].min(),
                                'feature_max': feature_values_for_analysis[region_mask].max()
                            }
                        else:
                             feature_regional_data[region_name] = {'r2': np.nan, 'n_samples': len(y_true_region)}
                    else:
                        feature_regional_data[region_name] = {'r2': np.nan, 'n_samples': np.sum(region_mask)}
                regional_analysis_results[feature_name] = feature_regional_data
            self.combined_results[target]['regional_analysis'] = regional_analysis_results
            print(f"Regional R² analysis completed for {target}.")


    def create_visualizations(self):
        """Create comprehensive visualizations."""
        print("\nCreating visualizations...")
        try:
            plt.style.use('seaborn-v0_8-whitegrid') # Using a seaborn style (hyphen instead of underscore)
        except OSError:
            print("Warning: Style 'seaborn-v0_8-whitegrid' not found. Trying 'seaborn-whitegrid'.")
            plt.style.use('seaborn-whitegrid') # Fallback style
        sns.set_palette("husl")

        for target in self.config.Y_COLS:
            if not self.combined_results[target]['y_true_all']:
                print(f"Skipping visualizations for {target} due to no combined results.")
                continue

            y_true = np.array(self.combined_results[target]['y_true_all'])
            y_pred = np.array(self.combined_results[target]['y_pred_all'])
            fold_indices = np.array(self.combined_results[target]['fold_indices_all'])

            # --- 1. Combined Scatter Plot & Core Metrics ---
            fig_main, axes_main = plt.subplots(2, 2, figsize=(16, 14))
            fig_main.suptitle(f'PySR Analysis Summary for Target: {target}', fontsize=18, fontweight='bold')

            # Scatter plot of True vs. Predicted (Combined)
            ax = axes_main[0, 0]
            sc = ax.scatter(y_true, y_pred, c=fold_indices, cmap='viridis', alpha=0.7, edgecolors='k', linewidth=0.5)
            ax.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2, label='Ideal Fit')
            ax.set_xlabel('True Values', fontsize=12)
            ax.set_ylabel('Predicted Values', fontsize=12)
            ax.set_title('Combined True vs. Predicted (All Folds)', fontsize=14)
            ax.legend()
            cbar = fig_main.colorbar(sc, ax=ax, label='Fold Index')
            if self.combined_results[target].get('combined_metrics'):
                r2_comb = self.combined_results[target]['combined_metrics']['r2']
                ax.text(0.05, 0.95, f'$R^2_{{comb}}$ = {r2_comb:.3f}', transform=ax.transAxes,
                        fontsize=12, va='top', bbox=dict(boxstyle='round,pad=0.5', fc='wheat', alpha=0.7))

            # Fold-wise R² Bar Plot
            ax = axes_main[0, 1]
            fold_r2s = [res['r2'] for res in self.results[target]['fold_results'] if not np.isnan(res['r2'])]
            if fold_r2s:
                ax.bar(range(len(fold_r2s)), fold_r2s, alpha=0.8, color=sns.color_palette("viridis", len(fold_r2s)))
                ax.set_xlabel('Fold', fontsize=12)
                ax.set_ylabel('$R^2$ Score', fontsize=12)
                ax.set_title('Fold-wise $R^2$ Scores', fontsize=14)
                ax.set_xticks(range(len(fold_r2s)))
                ax.set_xticklabels([f'{i+1}' for i in range(len(fold_r2s))])
                ax.axhline(np.mean(fold_r2s), color='r', linestyle='--', label=f'Mean $R^2$: {np.mean(fold_r2s):.3f}')
                ax.legend()
            else:
                ax.text(0.5, 0.5, 'No valid R² scores for folds.', ha='center', va='center', transform=ax.transAxes)

            # Residuals Plot
            ax = axes_main[1, 0]
            residuals = y_true - y_pred
            ax.scatter(y_pred, residuals, alpha=0.6, edgecolors='k', linewidth=0.5)
            ax.axhline(y=0, color='r', linestyle='--')
            ax.set_xlabel('Predicted Values', fontsize=12)
            ax.set_ylabel('Residuals (True - Predicted)', fontsize=12)
            ax.set_title('Residuals vs. Predicted Values', fontsize=14)

            # Distribution Comparison
            ax = axes_main[1, 1]
            sns.histplot(y_true, color="dodgerblue", label='True Values', kde=True, ax=ax, stat="density", common_norm=False)
            sns.histplot(y_pred, color="orange", label='Predicted Values', kde=True, ax=ax, stat="density", common_norm=False)
            ax.set_xlabel('Values', fontsize=12)
            ax.set_ylabel('Density', fontsize=12)
            ax.set_title('Distribution of True vs. Predicted Values', fontsize=14)
            ax.legend()

            plt.tight_layout(rect=[0, 0, 1, 0.96]) # Adjust for suptitle
            plt.savefig(os.path.join(self.output_dir_plots, f'{target}_main_summary_plots.png'), dpi=300)
            plt.close(fig_main)

            # --- 2. Individual Fold Plots ---
            self.create_individual_fold_plots(target)
            
            # --- 3. Pareto Front Plot ---
            self.plot_pareto_front(target)

            # --- 4. Plot best equation from one fold (e.g., best R2 fold) against data ---
            best_fold_info = self.get_best_fold_info(target)
            if best_fold_info:
                best_model_for_target = best_fold_info['model']
                fold_idx = best_fold_info['fold_index']
                # Get original test data for this specific fold
                gkf = GroupKFold(n_splits=self.config.N_SPLITS_CV)
                _, test_indices_for_best_fold = list(gkf.split(self.X, self.groups, groups=self.groups))[fold_idx]
                
                X_test_best_fold = self.X[test_indices_for_best_fold]
                y_test_best_fold = self.y_dict[target][test_indices_for_best_fold]
                
                if best_model_for_target and hasattr(best_model_for_target, 'predict'):
                    try:
                        best_eq_str = self.results[target]['best_equations_str'][fold_idx]
                        if best_eq_str and "FAILED" not in best_eq_str:
                             self.plot_equation_vs_data(
                                best_eq_str,
                                X_test_best_fold,
                                y_test_best_fold,
                                self.feature_names,
                                f"Best Equation (Fold {fold_idx+1}) for {target}",
                                os.path.join(self.output_dir_plots, f'{target}_best_fold_eq_fit.png')
                            )
                    except Exception as e:
                        print(f"Could not plot best equation for {target}: {e}")

        print("Visualizations created.")

    def get_best_fold_info(self, target):
        """Helper to get model and index of the best performing fold for a target."""
        best_r2 = -np.inf
        best_model = None
        best_fold_idx = -1
        
        if target not in self.results or not self.results[target]['fold_results']:
            return None

        for i, fold_res in enumerate(self.results[target]['fold_results']):
            if not np.isnan(fold_res['r2']) and fold_res['r2'] > best_r2:
                best_r2 = fold_res['r2']
                best_model = self.results[target]['models'][i]
                best_fold_idx = i
        
        if best_model:
            return {'model': best_model, 'fold_index': best_fold_idx, 'r2': best_r2}
        return None

    def create_individual_fold_plots(self, target):
        """Create individual scatter plots for each fold's predictions."""
        fold_results_list = self.results[target]['fold_results']
        n_folds = len(fold_results_list)
        if n_folds == 0: return

        cols = min(3, n_folds)
        rows = (n_folds + cols - 1) // cols
        fig, axes = plt.subplots(rows, cols, figsize=(5 * cols, 4.5 * rows), squeeze=False)
        fig.suptitle(f'Individual Fold True vs. Predicted for Target: {target}', fontsize=16, fontweight='bold')

        for i, result_data in enumerate(fold_results_list):
            ax = axes[i // cols, i % cols]
            if not np.isnan(result_data['r2']):
                y_t, y_p = result_data['y_true'], result_data['y_pred']
                # Filter out NaNs from predictions for plotting this fold
                valid_mask = ~(np.isnan(y_t) | np.isnan(y_p))
                y_t_clean, y_p_clean = y_t[valid_mask], y_p[valid_mask]

                if len(y_t_clean) > 0:
                    ax.scatter(y_t_clean, y_p_clean, alpha=0.7, edgecolors='k', linewidth=0.5)
                    ax.plot([y_t_clean.min(), y_t_clean.max()], [y_t_clean.min(), y_t_clean.max()], 'r--', lw=1.5)
                    ax.set_title(f'Fold {i+1}\n$R^2$ = {result_data["r2"]:.3f}', fontsize=10)
                else:
                    ax.text(0.5, 0.5, 'No valid data points for this fold', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'Fold {i+1} (No valid data)', fontsize=10)
            else:
                ax.text(0.5, 0.5, 'Training/Eval Failed', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'Fold {i+1} (Failed)', fontsize=10)
            ax.set_xlabel('True Values', fontsize=9)
            ax.set_ylabel('Predicted Values', fontsize=9)

        for i in range(n_folds, rows * cols): # Hide unused subplots
            axes[i // cols, i % cols].set_visible(False)

        plt.tight_layout(rect=[0, 0, 1, 0.95])
        plt.savefig(os.path.join(self.output_dir_plots, f'{target}_individual_fold_plots.png'), dpi=300)
        plt.close(fig)

    def plot_pareto_front(self, target):
        """Plots the complexity vs. score for equations from all folds of a target."""
        print(f"Plotting Pareto front for {target}...")
        all_equations_df = pd.concat(
            [df for df in self.results[target]['equations_dfs'] if df is not None and not df.empty],
            ignore_index=True
        )

        if all_equations_df.empty or 'complexity' not in all_equations_df.columns or 'loss' not in all_equations_df.columns:
            print(f"  No equations data available to plot Pareto front for {target}.")
            return

        # Ensure 'score' column exists, if not, it might be related to 'loss'
        if 'score' not in all_equations_df.columns and 'loss' in all_equations_df.columns:
             all_equations_df['score'] = all_equations_df['loss'] # Or however score is defined if different

        # Sort by complexity, then by score (lower is better for loss/score)
        df_sorted = all_equations_df.sort_values(by=['complexity', 'score'])
        pareto_front = []
        min_score_at_complexity = float('inf')

        # Identify Pareto front (simple version for non-dominated solutions)
        # A point (c, s) is on the Pareto front if there's no other point (c', s')
        # such that c' <= c and s' < s, or c' < c and s' <= s.
        # For simplicity here, we plot all points and highlight potential candidates.
        # A true Pareto front identification is more involved.
        
        # We'll just plot all unique complexity/score pairs
        unique_points = df_sorted.drop_duplicates(subset=['complexity', 'score'])

        plt.figure(figsize=(10, 7))
        scatter = plt.scatter(unique_points['complexity'], unique_points['score'], 
                              alpha=0.7, c=unique_points['score'], cmap='viridis_r', s=50) # smaller score is better
        
        # Annotate some points (e.g., overall best score)
        if not unique_points.empty:
            best_overall_score_point = unique_points.loc[unique_points['score'].idxmin()]
            plt.scatter(best_overall_score_point['complexity'], best_overall_score_point['score'],
                        color='red', s=100, edgecolor='black', zorder=5, label='Best Score')
            plt.annotate(f"Best Score (C:{best_overall_score_point['complexity']}, S:{best_overall_score_point['score']:.3e})",
                         (best_overall_score_point['complexity'], best_overall_score_point['score']),
                         textcoords="offset points", xytext=(0,10), ha='center', color='red')

        plt.xlabel('Complexity', fontsize=12)
        plt.ylabel('Score (Loss + Parsimony)', fontsize=12) # PySR 'score' column
        plt.title(f'Equation Complexity vs. Score (Pareto Front Candidates) for {target}', fontsize=14)
        plt.colorbar(scatter, label='Score')
        plt.grid(True, linestyle=':', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir_plots, f'{target}_pareto_front.png'), dpi=300)
        plt.close()

    def plot_equation_vs_data(self, equation_str, X_data, y_true_data, feature_names, title, save_path):
        """Plots a given SymPy equation's predictions against true data."""
        print(f"Plotting equation: {equation_str[:100]}...")
        if "FAILED" in equation_str:
            print("  Skipping plot for failed equation.")
            return

        try:
            # Create a dictionary of SymPy symbols for each feature
            symbols_dict = {name: sympy.Symbol(name) for name in feature_names}
            # Sympify the equation string
            equation_sympy = sympy.sympify(equation_str, locals=symbols_dict)
            
            # Lambdify the SymPy equation for numerical evaluation
            # The order of arguments in lambdify must match the order of feature_names
            func = sympy.lambdify([symbols_dict[name] for name in feature_names], equation_sympy, modules=['numpy'])
            
            # Prepare X_data for func (it expects individual arguments or an array to be unpacked)
            # X_data is (n_samples, n_features). We need to pass it as func(X[:,0], X[:,1], ...)
            y_pred_eq = func(*[X_data[:, i] for i in range(X_data.shape[1])])

        except Exception as e:
            print(f"  Error evaluating equation '{equation_str[:60]}...': {e}")
            return

        plt.figure(figsize=(8, 6))
        plt.scatter(y_true_data, y_pred_eq, alpha=0.6, edgecolors='k', linewidth=0.5, label='Equation Predictions')
        plt.plot([y_true_data.min(), y_true_data.max()], [y_true_data.min(), y_true_data.max()], 'r--', lw=2, label='Ideal Fit')
        plt.xlabel('True Values', fontsize=12)
        plt.ylabel('Predicted Values by Equation', fontsize=12)
        plt.title(title, fontsize=14)
        
        # Calculate R² for this specific equation fit
        try:
            r2_eq = r2_score(y_true_data, y_pred_eq)
            plt.text(0.05, 0.95, f'$R^2$ = {r2_eq:.3f}', transform=plt.gca().transAxes,
                     fontsize=12, va='top', bbox=dict(boxstyle='round,pad=0.5', fc='wheat', alpha=0.7))
        except ValueError as e_r2: # Catch errors if y_pred_eq is all NaN or constant, or other r2_score issues
            print(f"  Note: R² for equation plot '{title}' could not be calculated: {e_r2}")
            # Optionally, add a text to the plot indicating R2 is N/A

        plt.legend()
        plt.grid(True, linestyle=':', alpha=0.7)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300)
        plt.close()
        print(f"  Equation plot saved to {save_path}")

    def _calculate_skewness(self, data):
        """Calculate skewness of data."""
        n = len(data)
        if n < 3: return np.nan
        mean_val = np.mean(data)
        std_val = np.std(data, ddof=0) # Population std for formula
        if std_val == 0: return 0 if np.all(data == mean_val) else np.nan # All same or problematic
        # Using formula for sample skewness (adjusts for bias)
        # G1 = (n / ((n-1)*(n-2))) * sum(((data - mean_val)/std_val_sample)**3) where std_val_sample uses ddof=1
        # Simpler: use scipy.stats.skew if available, or stick to a basic moment calc
        # For consistency with common definitions, let's use population moments:
        m3 = np.mean((data - mean_val)**3)
        m2 = np.mean((data - mean_val)**2)
        if m2 == 0: return 0 if np.all(data == mean_val) else np.nan
        return m3 / (m2**1.5)


    def _calculate_kurtosis(self, data, fisher=True):
        """Calculate kurtosis (Fisher's definition by default - excess kurtosis)."""
        n = len(data)
        if n < 4: return np.nan
        mean_val = np.mean(data)
        std_val = np.std(data, ddof=0) # Population std
        if std_val == 0: return -3 if fisher and np.all(data==mean_val) else (0 if not fisher and np.all(data==mean_val) else np.nan)

        m4 = np.mean((data - mean_val)**4)
        m2 = np.mean((data - mean_val)**2)
        if m2 == 0: return -3 if fisher and np.all(data==mean_val) else (0 if not fisher and np.all(data==mean_val) else np.nan)
        
        kurt = m4 / (m2**2)
        return kurt - 3 if fisher else kurt

    def _test_normality_shapiro(self, data):
        """Normality test using Shapiro-Wilk."""
        if len(data) < 3 or len(data) > 5000: # Shapiro-Wilk limitations
            return {'statistic': np.nan, 'p_value': np.nan, 'is_normal_alpha_0.05': None, 'comment': 'Sample size out of range for Shapiro-Wilk'}
        try:
            stat, p_value = shapiro(data)
            return {'statistic': stat, 'p_value': p_value, 'is_normal_alpha_0.05': p_value > 0.05}
        except Exception as e:
            return {'statistic': np.nan, 'p_value': np.nan, 'is_normal_alpha_0.05': None, 'comment': str(e)}

    def _get_feature_counts_from_sympy_eq(self, equation_str, feature_names_list):
        """Parse a SymPy equation string and count occurrences of features."""
        if "FAILED" in equation_str: return {feat: 0 for feat in feature_names_list}
        try:
            # Create a dictionary of SymPy symbols for each feature
            symbols_dict = {name: sympy.Symbol(name) for name in feature_names_list}
            # Sympify the equation string
            expr = sympy.sympify(equation_str, locals=symbols_dict)
            
            counts = Counter()
            for atom in expr.atoms(sympy.Symbol):
                if str(atom) in feature_names_list:
                    counts[str(atom)] += 1
            return counts
        except Exception as e:
            print(f"  Warning: Could not parse equation for feature counts: {equation_str[:60]}... Error: {e}")
            return {feat: 0 for feat in feature_names_list}

    def _analyze_feature_importance(self, target):
        """Analyze feature importance based on frequency in discovered equations (parsed)."""
        all_best_equations_str = self.results[target]['best_equations_str']
        valid_equations = [eq for eq in all_best_equations_str if eq and "FAILED" not in eq]

        if not valid_equations:
            return {'feature_frequency': {}, 'most_important': (None, 0), 'least_important': (None, 0)}

        total_feature_counts = Counter()
        for eq_str in valid_equations:
            counts_for_eq = self._get_feature_counts_from_sympy_eq(eq_str, self.feature_names)
            total_feature_counts.update(counts_for_eq)
        
        num_valid_eq = len(valid_equations)
        # Average frequency per equation
        feature_avg_frequency = {
            feat: total_feature_counts.get(feat, 0) / num_valid_eq for feat in self.feature_names
        }
        
        # Normalize to sum to 1 (relative importance)
        total_counts_sum = sum(total_feature_counts.values())
        feature_relative_importance = {
            feat: total_feature_counts.get(feat, 0) / total_counts_sum if total_counts_sum > 0 else 0
            for feat in self.feature_names
        }

        sorted_importance = sorted(feature_relative_importance.items(), key=lambda x: x[1], reverse=True)

        return {
            'feature_average_frequency': feature_avg_frequency,
            'feature_relative_importance': feature_relative_importance,
            'most_important_features': sorted_importance[:self.config.TOP_N_FEATURES_TO_DISPLAY_IMPORTANCE],
            'least_important_features': sorted_importance[-self.config.TOP_N_FEATURES_TO_DISPLAY_IMPORTANCE:]
        }

    def enhanced_model_validation(self):
        """Perform enhanced model validation."""
        print("\nPerforming enhanced model validation...")
        for target in self.config.Y_COLS:
            if not self.combined_results[target]['y_true_all']:
                print(f"Skipping validation for {target} due to no combined results.")
                self.validation_results[target] = {}
                continue
            
            print(f"\nValidating models for target: {target}...")
            current_target_validation = {}

            # 1. Residual Analysis (on combined predictions)
            y_true = np.array(self.combined_results[target]['y_true_all'])
            y_pred = np.array(self.combined_results[target]['y_pred_all'])
            residuals = y_true - y_pred
            current_target_validation['residual_analysis'] = {
                'mean_residual': np.mean(residuals),
                'std_residual': np.std(residuals),
                'skewness': self._calculate_skewness(residuals),
                'kurtosis': self._calculate_kurtosis(residuals), # Fisher's (excess)
                'normality_shapiro_wilk': self._test_normality_shapiro(residuals)
            }

            # 2. Equation Complexity Analysis (from PySR's output)
            all_eq_dfs = self.results[target]['equations_dfs']
            valid_eq_dfs = [df for df in all_eq_dfs if df is not None and not df.empty and 'complexity' in df.columns]
            if valid_eq_dfs:
                all_complexities = pd.concat(valid_eq_dfs)['complexity'].dropna().tolist()
                if all_complexities:
                    current_target_validation['equation_complexity_pysr'] = {
                        'mean_complexity': np.mean(all_complexities),
                        'std_complexity': np.std(all_complexities),
                        'min_complexity': np.min(all_complexities),
                        'max_complexity': np.max(all_complexities),
                        'median_complexity': np.median(all_complexities)
                    }

            # 3. Feature Importance (based on frequency in best equations from folds)
            current_target_validation['feature_importance'] = self._analyze_feature_importance(target)

            # 4. Model Stability Analysis (R² variation across folds)
            fold_r2s = [res['r2'] for res in self.results[target]['fold_results'] if not np.isnan(res['r2'])]
            if len(fold_r2s) >= 2:
                mean_r2, std_r2 = np.mean(fold_r2s), np.std(fold_r2s)
                cv_r2 = std_r2 / abs(mean_r2) if mean_r2 != 0 else np.inf
                current_target_validation['model_stability_r2'] = {
                    'mean_fold_r2': mean_r2,
                    'std_fold_r2': std_r2,
                    'cv_fold_r2': cv_r2, # Coefficient of Variation
                    'min_fold_r2': np.min(fold_r2s),
                    'max_fold_r2': np.max(fold_r2s),
                    'num_successful_folds': len(fold_r2s)
                }
            else:
                 current_target_validation['model_stability_r2'] = {'comment': 'Not enough successful folds for stability analysis.'}
            
            self.validation_results[target] = current_target_validation
        print("Enhanced model validation completed.")


    def export_results_summary(self):
        """Export a text summary of the analysis results."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = os.path.join(self.output_dir_results, f'analysis_summary_{timestamp}.txt')
        
        with open(summary_file, 'w') as f:
            f.write(f"PySR Comprehensive Analysis Summary\n")
            f.write(f"Timestamp: {timestamp}\n")
            f.write(f"Data Path: {self.config.DATA_PATH}\n")
            f.write("=" * 70 + "\n\n")

            f.write("Configuration Highlights:\n")
            f.write(f"- Enhanced Features Used: {self.config.USE_ENHANCED_FEATURES}\n")
            f.write(f"- Number of CV Folds: {self.config.N_SPLITS_CV}\n")
            f.write(f"- PySR Iterations: {self.config.PYSR_NITERATIONS}\n")
            f.write(f"- PySR Max Complexity: {self.config.PYSR_MAXSIZE}\n")
            f.write(f"- Features Considered ({len(self.feature_names)}): {', '.join(self.feature_names)}\n\n")

            for target in self.config.Y_COLS:
                f.write(f"--- Target: {target} ---\n")
                
                # Combined Metrics
                if self.combined_results[target].get('combined_metrics'):
                    metrics = self.combined_results[target]['combined_metrics']
                    f.write("Combined Cross-Validation Metrics (on all test folds):\n")
                    f.write(f"  R²: {metrics['r2']:.4f}\n")
                    f.write(f"  MSE: {metrics['mse']:.4f}\n")
                    f.write(f"  MAE: {metrics['mae']:.4f}\n")
                    f.write(f"  Number of Samples: {metrics['n_samples']}\n\n")
                else:
                    f.write("Combined metrics not available.\n\n")

                # Best Equations from each fold
                f.write("Best Equations from Each Fold (simplest representation):\n")
                for i, eq_str in enumerate(self.results[target]['best_equations_str']):
                    fold_r2 = self.results[target]['fold_results'][i]['r2']
                    fold_comp = "N/A"
                    if self.results[target]['equations_dfs'][i] is not None and \
                       not self.results[target]['equations_dfs'][i].empty and \
                       'complexity' in self.results[target]['equations_dfs'][i].columns:
                        try:
                            # Get complexity of the *best* equation for that fold
                            pysr_model_fold = self.results[target]['models'][i] # PySR model from that fold
                            if pysr_model_fold: # Ensure model object exists
                                best_eq_df_fold = pysr_model_fold.get_best() 
                                if not best_eq_df_fold.empty:
                                    fold_comp = best_eq_df_fold['complexity'].iloc[0]
                        except: pass # Ignore if can't get complexity
                    
                    f.write(f"  Fold {i+1} (R²={fold_r2:.3f}, Complexity={fold_comp}): {eq_str[:120]}...\n")
                f.write("\n")

                # Validation Summary
                if target in self.validation_results and self.validation_results[target]:
                    val_res = self.validation_results[target]
                    f.write("Validation Insights:\n")
                    if 'model_stability_r2' in val_res:
                        stab = val_res['model_stability_r2']
                        f.write(f"  Model Stability (R² across folds): Mean={stab.get('mean_fold_r2', np.nan):.3f}, Std={stab.get('std_fold_r2', np.nan):.3f}, CV={stab.get('cv_fold_r2', np.nan):.3f}\n")
                    
                    if 'residual_analysis' in val_res:
                        res_an = val_res['residual_analysis']
                        normality = res_an['normality_shapiro_wilk']
                        f.write(f"  Residuals: Mean={res_an['mean_residual']:.3e}, Skew={res_an['skewness']:.3f}, Kurtosis={res_an['kurtosis']:.3f}\n")
                        f.write(f"    Normality (Shapiro P-value): {normality['p_value']:.3e} (Normal if >0.05: {normality['is_normal_alpha_0.05']})\n")

                    if 'feature_importance' in val_res and val_res['feature_importance']['most_important_features']:
                        f.write("  Top Important Features (relative importance in equations):\n")
                        for feat, imp in val_res['feature_importance']['most_important_features']:
                            if imp > 0: # Only show if some importance
                                f.write(f"    - {feat}: {imp:.3f}\n")
                    f.write("\n")
                
                # Regional R2 Summary (Top few)
                if 'regional_analysis' in self.combined_results[target] and self.combined_results[target]['regional_analysis']:
                    f.write("Regional R² Highlights (examples):\n")
                    count = 0
                    for feature, regions_data in self.combined_results[target]['regional_analysis'].items():
                        if count >= 2: break # Show for first 2 features
                        f.write(f"  For feature '{feature}':\n")
                        sub_count = 0
                        for region_name, data in regions_data.items():
                            if sub_count >=2: break # Show first 2 quartiles
                            if not np.isnan(data['r2']):
                                f.write(f"    {region_name}: R²={data['r2']:.3f} (N={data['n_samples']})\n")
                            sub_count +=1
                        count +=1
                    f.write("\n")

                f.write("-" * 50 + "\n\n")
        print(f"Analysis summary exported to {summary_file}")

    def export_detailed_results_pickle(self):
        """Export detailed results, models, and configurations to a pickle file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pickle_file = os.path.join(self.output_dir_results, f'detailed_analysis_results_{timestamp}.pkl')
        
        # We cannot directly pickle PySR models if they contain complex Julia objects
        # that are not serializable by Python's pickle.
        # So, we'll store paths to saved model files or just the equations.
        # For this version, we are saving models separately.
        # The 'models' in self.results might not be fully pickleable.
        # Let's try to pickle what we can, and remove models if it fails.
        
        export_data = {
            'config': self.config, # The config object itself might not be ideal, better to store as dict
            'config_dict': vars(self.config) if hasattr(self.config, '__dict__') else str(self.config),
            'results_summary': self.results, # Contains fold_results, equations_dfs, best_equations_str
            'combined_results': self.combined_results,
            'validation_results': self.validation_results,
            'feature_names': self.feature_names,
            'timestamp': timestamp
        }
        
        # Temporarily remove model objects from results_summary for pickling if they cause issues
        # A more robust way is to save model equations and parameters, not the model object itself in the pickle.
        # PySR models can be tricky to pickle directly.
        # Let's attempt to save. If it fails, we can try removing the 'models' part.
        
        # For now, we rely on save_individual_models for model persistence.
        # The 'models' list in self.results contains the PySRRegressor objects.
        # Let's create a copy of results and remove the models before pickling.
        
        results_for_pickle = {}
        for target, res_data in self.results.items():
            results_for_pickle[target] = {k: v for k, v in res_data.items() if k != 'models'}
        export_data['results_summary_no_models'] = results_for_pickle


        try:
            with open(pickle_file, 'wb') as f:
                pickle.dump(export_data, f)
            print(f"Detailed results (excluding direct model objects) exported to {pickle_file}")
        except Exception as e:
            print(f"Error pickling detailed results: {e}. Results may not be fully saved.")
            # Fallback: try pickling without the potentially problematic 'results_summary'
            try:
                del export_data['results_summary'] # if it was the issue
                del export_data['results_summary_no_models']
                with open(pickle_file.replace(".pkl", "_partial.pkl"), 'wb') as f:
                    pickle.dump(export_data, f)
                print(f"Partially detailed results exported to {pickle_file.replace('.pkl', '_partial.pkl')}")
            except Exception as e2:
                print(f"Further error pickling partial results: {e2}")


    def save_individual_models(self):
        """Save the best performing PySR model for each target to a .pkl file.
           Note: PySR models might be tricky to save and load if they rely on live Julia state.
           Saving the equation string is often more robust for long-term use.
           This attempts to save the model object.
        """
        print("\nSaving best model objects (experimental due to PySR pickling)...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for target in self.config.Y_COLS:
            best_fold_info = self.get_best_fold_info(target)
            if best_fold_info and best_fold_info['model']:
                model_to_save = best_fold_info['model']
                fold_idx = best_fold_info['fold_index']
                r2_val = best_fold_info['r2']
                
                model_filename = os.path.join(self.output_dir_models, f'{target}_best_model_fold{fold_idx+1}_{timestamp}.pkl')
                try:
                    # Before saving, ensure temp files related to this model are handled if needed
                    # PySR models might store references to temp files.
                    # It's safer to rely on the hall_of_fame / .csv equation files PySR produces.
                    # However, we attempt to pickle the regressor object.
                    with open(model_filename, 'wb') as f:
                        pickle.dump(model_to_save, f)
                    print(f"  Saved best model object for {target} (Fold {fold_idx+1}, R²={r2_val:.4f}) to {model_filename}")
                    print(f"  Associated equations CSV: equations_{target}_fold_{fold_idx+1}.csv (if created by PySR)")
                except Exception as e:
                    print(f"  Warning: Could not pickle model for {target}. Error: {e}")
                    print(f"  Consider using the equation string: {self.results[target]['best_equations_str'][fold_idx]}")
            else:
                print(f"  No best model found or saved for target {target}.")


    def run_complete_analysis(self):
        """Run the complete PySR analysis pipeline."""
        print("Starting comprehensive PySR analysis with enhanced features...")
        print(f"Output base directory: {self.config.OUTPUT_DIR_BASE}")
        
        try:
            self.perform_cross_validation()
            self.calculate_combined_metrics()
            self.analyze_regional_r2() # Uses combined results
            self.enhanced_model_validation() # Uses combined and fold results
            self.create_visualizations() # Uses all results
            
            self.export_results_summary()
            self.export_detailed_results_pickle()
            self.save_individual_models() # Experimental

            print("\n" + "="*70)
            print("ENHANCED ANALYSIS PIPELINE COMPLETED!")
            print("="*70 + "\n")

            print("Summary of Combined Metrics:")
            for target in self.config.Y_COLS:
                if self.combined_results[target].get('combined_metrics'):
                    metrics = self.combined_results[target]['combined_metrics']
                    print(f"  Target '{target}': R² = {metrics['r2']:.4f}, MSE = {metrics['mse']:.4f}, MAE = {metrics['mae']:.4f}")
                else:
                    print(f"  Target '{target}': No valid combined metrics.")
            
            print(f"\nAll outputs saved in subdirectories under: {self.config.OUTPUT_DIR_BASE}")

        except Exception as e:
            print(f"\nFATAL ERROR during analysis pipeline: {str(e)}")
            import traceback
            traceback.print_exc()
            print("\nAnalysis terminated prematurely.")


# Configuration class to hold all settings
class AnalysisConfig:
    def __init__(self):
        self.DATA_PATH = DATA_PATH
        self.OUTPUT_DIR_BASE = OUTPUT_DIR_BASE
        self.X_COLS_BASE = X_COLS_BASE
        self.MICRO_ODOSIMETRY_COLS = MICRO_ODOSIMETRY_COLS
        self.Y_COLS = Y_COLS
        self.GROUP_COLS = GROUP_COLS
        self.USE_ENHANCED_FEATURES = USE_ENHANCED_FEATURES
        self.N_SPLITS_CV = N_SPLITS_CV
        self.PYSR_NITERATIONS = PYSR_NITERATIONS
        self.PYSR_POPULATIONS = PYSR_POPULATIONS
        self.PYSR_POPULATION_SIZE = PYSR_POPULATION_SIZE
        self.PYSR_MAXSIZE = PYSR_MAXSIZE
        self.PYSR_TIMEOUT_IN_SECONDS = PYSR_TIMEOUT_IN_SECONDS
        self.PYSR_PARSIMONY = PYSR_PARSIMONY
        self.PYSR_BINARY_OPERATORS = PYSR_BINARY_OPERATORS
        self.PYSR_UNARY_OPERATORS = PYSR_UNARY_OPERATORS
        self.PYSR_EXTRA_SYMPY_MAPPINGS = PYSR_EXTRA_SYMPY_MAPPINGS
        self.PYSR_COMPLEXITY_OF_OPERATORS = PYSR_COMPLEXITY_OF_OPERATORS
        self.PYSR_LOSS_FUNCTION = PYSR_LOSS_FUNCTION
        self.PYSR_PROCS = PYSR_PROCS
        self.PYSR_DELETE_TEMPFILES = PYSR_DELETE_TEMPFILES
        self.PYSR_RANDOM_STATE = PYSR_RANDOM_STATE
        self.PYSR_VERBOSITY = PYSR_VERBOSITY
        self.PYSR_PROGRESS = PYSR_PROGRESS
        self.MIN_SAMPLES_FOR_REGIONAL_R2 = MIN_SAMPLES_FOR_REGIONAL_R2
        self.TOP_N_FEATURES_TO_DISPLAY_IMPORTANCE = TOP_N_FEATURES_TO_DISPLAY_IMPORTANCE

def main():
    """Main execution function."""
    print("Initializing Enhanced PySR Comprehensive Analysis Script...")
    
    # Create an instance of the configuration
    config = AnalysisConfig()

    # Initialize and run the analysis
    analyzer = PySRComprehensiveAnalysis(config)
    analyzer.run_complete_analysis()
    
    print("\n" + "="*70)
    print("SCRIPT EXECUTION FINISHED.")
    print("="*70)
    print("\nKey Enhancements Implemented:")
    print("✓ Centralized configuration for easy tuning.")
    print("✓ Customizable feature engineering.")
    print("✓ Custom PySR operators (e.g., sigmoid, square, cube, gaussian_like).")
    print("✓ More detailed PySR parameter settings exposed.")
    print("✓ Advanced model validation (residuals, complexity, stability, feature importance).")
    print("✓ Improved feature importance using SymPy parsing.")
    print("✓ Additional visualizations (Pareto front, individual equation fits).")
    print("✓ Structured output directories for results, plots, and models.")
    print("\nRecommendation: Review the generated text summaries, plots (especially Pareto fronts),")
    print("and detailed validation results for model selection and interpretation.")

if __name__ == "__main__":
    # Example: Create a dummy CSV for testing if Data_With_stats.csv doesn't exist
    if not os.path.exists(DATA_PATH):
        print(f"Warning: Data file '{DATA_PATH}' not found. Creating a dummy CSV for testing purposes.")
        num_samples = 100
        num_micro_cols = len(MICRO_ODOSIMETRY_COLS)
        dummy_data = {
            'DNAcontent': np.random.rand(num_samples) * 10,
            'amu': np.random.rand(num_samples) * 50 + 1,
            'LET': np.random.rand(num_samples) * 100 + 0.1,
            'Di': np.random.rand(num_samples) * 5,
            **{col: np.random.rand(num_samples) for col in MICRO_ODOSIMETRY_COLS},
            'SFi': np.exp(-(np.random.rand(num_samples)*0.2 * (np.random.rand(num_samples) * 5))), # Dummy SFi
            'RBE10': np.random.rand(num_samples) * 3 + 1, # Dummy RBE10
            # 'MIDi': np.random.rand(num_samples) * 2 # Uncomment if MIDi is a target
        }
        # Ensure group columns have some discrete variation for GroupKFold
        dummy_data['DNAcontent'] = np.random.choice([2,4,6,8], num_samples)
        dummy_data['amu'] = np.random.choice([1,12,16,40], num_samples)
        dummy_data['LET'] = np.random.choice([0.2, 1, 10, 50, 100], num_samples)

        dummy_df = pd.DataFrame(dummy_data)
        dummy_df.to_csv(DATA_PATH, index=False)
        print(f"Dummy '{DATA_PATH}' created with {num_samples} samples.")

    main()
